{"hash": "33e79499", "browserHash": "3745e032", "optimized": {"react": {"src": "../../.pnpm/react@18.3.1/node_modules/react/index.js", "file": "react.js", "fileHash": "6447f194", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "1da448dd", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@18.3.1/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "099ae227", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@18.3.1/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f80e34ef", "needsInterop": true}, "@radix-ui/react-slot": {"src": "../../.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2f20d3b2", "needsInterop": false}, "class-variance-authority": {"src": "../../.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2afe258a", "needsInterop": false}, "clsx": {"src": "../../.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "cb056a54", "needsInterop": false}, "framer-motion": {"src": "../../.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "3812a839", "needsInterop": false}, "lucide-react": {"src": "../../.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "44daf8ce", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c528f856", "needsInterop": true}, "sonner": {"src": "../../.pnpm/sonner@2.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "48aff3f3", "needsInterop": false}, "tailwind-merge": {"src": "../../.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "1ad8240b", "needsInterop": false}}, "chunks": {"chunk-RU4HE4EE": {"file": "chunk-RU4HE4EE.js"}, "chunk-KD7YULJ4": {"file": "chunk-KD7YULJ4.js"}, "chunk-FV57N3A7": {"file": "chunk-FV57N3A7.js"}, "chunk-IQXJZDA7": {"file": "chunk-IQXJZDA7.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}